/**
 * 矩阵系统常量定义 - 完整的A-M组数据系统
 * 🎯 核心价值：33×33矩阵的完整数据源，支持9种颜色和4个层级，A-M组偏移系统
 * 📦 功能范围：坐标映射、颜色分组、层级管理、组偏移、数据查询
 * 🔄 架构设计：基于A组基础数据和偏移配置的高性能数据结构
 */

import type {
  BasicColorType,
  ColorValue,
  DataLevel,
  GroupOffsetConfig,
  GroupType
} from '../matrix/MatrixTypes';

// ===== 基础数据类型 =====

/** 矩阵数据点 */
export interface MatrixDataPoint {
  x: number;
  y: number;
  color: BasicColorType;
  level: DataLevel;
  group: GroupType;
  id: string;
}

/** 矩阵数据集合 */
export interface MatrixDataSet {
  points: MatrixDataPoint[];
  byColor: Map<BasicColorType, MatrixDataPoint[]>;
  byLevel: Map<DataLevel, MatrixDataPoint[]>;
  byGroup: Map<GroupType, MatrixDataPoint[]>;
  byCoordinate: Map<string, MatrixDataPoint>;
  metadata: {
    totalPoints: number;
    colorCounts: Record<BasicColorType, number>;
    levelCounts: Record<DataLevel, number>;
    groupCounts: Record<GroupType, number>;
    lastUpdate: number;
  };
}

// ===== A组基础数据结构 =====

/** A组基础数据结构 - 所有组数据的基础 */
export const GROUP_A_DATA = {
  black: {
    1: [[0, 0] as [number, number]]
  },
  red: {
    1: [[8, 0] as [number, number]],
    2: [[4, 0] as [number, number]],
    3: [[2, 0], [6, 0], [4, 2], [4, -2]] as [number, number][],
    4: [[1, 0], [3, 0], [5, 0], [7, 0], [2, 1], [2, -1], [3, 2], [3, -2], [4, 1], [4, 3], [4, -1], [4, -3], [6, 1], [6, -1], [5, 2], [5, -2]] as [number, number][]
  },
  orange: {
    1: [[4, -4] as [number, number]],
    3: [[6, 2], [2, -2], [-2, -6]] as [number, number][],
    4: [[-3, -5], [-1, -7], [-1, -5], [1, -1], [1, -3], [3, -3], [3, -1], [5, 3], [7, 1], [5, 1]] as [number, number][]
  },
  yellow: {
    1: [[0, -8] as [number, number]],
    2: [[0, -4] as [number, number]],
    3: [[0, -2], [0, -6], [2, -4], [-2, -4]] as [number, number][],
    4: [[0, -1], [-1, -2], [0, -3], [1, -2], [-2, -3], [-3, -4], [-2, -5], [-1, -4], [2, -3], [1, -4], [2, -5], [3, -4], [0, -5], [-1, -6], [0, -7], [1, -6]] as [number, number][]
  },
  green: {
    1: [[-4, -4] as [number, number]],
    3: [[-6, 2], [-2, -2], [2, -6]] as [number, number][],
    4: [[-5, 3], [-7, 1], [-5, 1], [-1, -1], [-3, -1], [-3, -3], [-1, -3], [3, -5], [1, -5], [1, -7]] as [number, number][]
  },
  cyan: {
    1: [[-8, 0] as [number, number]],
    2: [[-4, 0] as [number, number]],
    3: [[-2, 0], [-6, 0], [-4, 2], [-4, -2]] as [number, number][],
    4: [[-7, 0], [-5, 0], [-3, 0], [-1, 0], [-6, 1], [-6, -1], [-5, 2], [-5, -2], [-4, 1], [-4, 3], [-4, -1], [-4, -3], [-2, 1], [-2, -1], [-3, 2], [-3, -2]] as [number, number][]
  },
  blue: {
    1: [[-4, 4] as [number, number]],
    3: [[2, 6], [-2, 2], [-6, -2]] as [number, number][],
    4: [[1, 7], [3, 5], [1, 5], [-3, 3], [-1, 3], [-1, 1], [-3, 1], [-7, -1], [-5, -1], [-5, -3]] as [number, number][]
  },
  purple: {
    1: [[0, 8] as [number, number]],
    2: [[0, 4] as [number, number]],
    3: [[0, 2], [0, 6], [2, 4], [-2, 4]] as [number, number][],
    4: [[0, 1], [0, 3], [0, 5], [0, 7], [1, 2], [1, 4], [1, 6], [-1, 2], [-1, 4], [-1, 6], [2, 3], [2, 5], [-2, 3], [-2, 5], [-3, 4], [3, 4]] as [number, number][]
  },
  pink: {
    1: [[4, 4] as [number, number]],
    3: [[-2, 6], [2, 2], [6, -2]] as [number, number][],
    4: [[-1, 7], [-1, 5], [-3, 5], [3, 3], [3, 1], [1, 1], [1, 3], [7, -1], [5, -3], [5, -1]] as [number, number][]
  }
} as const;

// ===== 可用级别映射 =====

/** 可用级别映射 - 统一的权威数据源 */
export const AVAILABLE_LEVELS: Record<BasicColorType, number[]> = {
  red: [1, 2, 3, 4],
  cyan: [1, 2, 3, 4],
  yellow: [1, 2, 3, 4],
  purple: [1, 2, 3, 4],
  orange: [1, 3, 4],
  green: [1, 3, 4],
  blue: [1, 3, 4],
  pink: [1, 3, 4],
  black: [1], // 黑色只有一个级别
} as const;

// ===== 默认颜色值定义 =====

/** 默认颜色值与颜色数字映射 */
export const DEFAULT_COLOR_VALUES: Record<BasicColorType, ColorValue> = {
  black: { name: '黑色', hex: '#000000', rgb: [0, 0, 0], hsl: [0, 0, 0] }, // 黑色没有mappingValue
  red: { name: '红色', hex: '#ef4444', rgb: [239, 68, 68], hsl: [0, 84, 60], mappingValue: 1 },
  cyan: { name: '青色', hex: '#06b6d4', rgb: [6, 182, 212], hsl: [189, 94, 43], mappingValue: 5 },
  yellow: { name: '黄色', hex: '#eab308', rgb: [234, 179, 8], hsl: [45, 93, 47], mappingValue: 3 },
  purple: { name: '紫色', hex: '#a855f7', rgb: [168, 85, 247], hsl: [271, 91, 65], mappingValue: 7 },
  orange: { name: '橙色', hex: '#f97316', rgb: [249, 115, 22], hsl: [25, 95, 53], mappingValue: 2 },
  green: { name: '绿色', hex: '#22c55e', rgb: [34, 197, 94], hsl: [142, 71, 45], mappingValue: 4 },
  blue: { name: '蓝色', hex: '#3b82f6', rgb: [59, 130, 246], hsl: [217, 91, 60], mappingValue: 6 },
  pink: { name: '粉色', hex: '#ec4899', rgb: [236, 72, 153], hsl: [327, 82, 60], mappingValue: 8 },
};

// ===== 矩阵计算常量 =====

export const MAX_LEVEL = 4;
export const MATRIX_SIZE = 33; // 33x33网格

/** 网格尺寸和中心点常量 */
export const GRID_DIMENSIONS = {
  ROWS: 33,
  COLS: 33
} as const;

export const GRID_CENTER = {
  X: 16, // 中心列索引 (33-1)/2 = 16
  Y: 16  // 中心行索引 (33-1)/2 = 16
} as const;

// ===== 扩展的A到M组偏移配置 =====

/** 扩展的A到M组偏移配置 - 基于对称性和规律性设计 */
export const GROUP_OFFSET_CONFIGS: Record<GroupType, GroupOffsetConfig> = {
  A: {
    defaultOffset: [0, 0], // A组无偏移，作为基础
  },
  B: {
    defaultOffset: [16, 0], // 向右偏移
    level1Offsets: {
      red: [16, 0],     // 右偏移
      orange: [8, 8],   // 右上偏移
      yellow: [16, 16], // 右上偏移
      green: [20, 0],   // 右偏移
      cyan: [24, 0],    // 右偏移
      blue: [20, 0],    // 右偏移
      purple: [16, -12],// 右下偏移
      pink: [8, -8],    // 右下偏移
      black: [16, 0],   // 右偏移
    }
  },
  C: {
    defaultOffset: [-16, 0], // 向左偏移
    level1Offsets: {
      red: [32, 0],     // 右偏移
      orange: [-24, 8], // 左上偏移
      yellow: [-16, 16],// 左上偏移
      green: [-8, 8],   // 左上偏移
      cyan: [0, 0],     // 无偏移
      blue: [-8, -8],   // 左下偏移
      purple: [-16, -16],// 左下偏移
      pink: [-24, -8],  // 左下偏移
      black: [-16, 0],  // 左偏移
    }
  },
  D: {
    defaultOffset: [0, 16],
    level1Offsets: {
      red: [-16, 16], orange: [-8, 24], yellow: [0, 32], green: [8, 24],
      cyan: [16, 16], blue: [8, 8], purple: [0, 0], pink: [-8, 8], black: [0, 16],
    }
  },
  E: {
    defaultOffset: [0, -16],
    level1Offsets: {
      red: [-16, -16], orange: [-8, -8], yellow: [0, -16], green: [8, -8],
      cyan: [16, -16], blue: [8, -24], purple: [-32, 0], pink: [-8, -24], black: [0, -16],
    }
  },
  F: {
    defaultOffset: [8, 8],
    level1Offsets: {
      red: [8, 8],      // 右上偏移
      orange: [16, 16], // 大右上偏移
      yellow: [8, 24],  // 上偏移
      green: [0, 16],   // 上偏移
      cyan: [-8, 8],    // 左上偏移
      blue: [0, 0],     // 无偏移
      purple: [8, -8],  // 右下偏移
      pink: [16, 0],    // 右偏移
      black: [8, 8],    // 右上偏移
    }
  },
  G: {
    defaultOffset: [-8, -8],
    level1Offsets: {
      red: [-8, -8],    // 左下偏移
      orange: [-16, -16], // 大左下偏移
      yellow: [-8, -24], // 下偏移
      green: [0, -16],   // 下偏移
      cyan: [8, -8],     // 右下偏移
      blue: [0, 0],      // 无偏移
      purple: [-8, 8],   // 左上偏移
      pink: [-16, 0],    // 左偏移
      black: [-8, -8],   // 左下偏移
    }
  },
  H: {
    defaultOffset: [8, -8],
    level1Offsets: {
      red: [8, -8],     // 右下偏移
      orange: [16, -16], // 大右下偏移
      yellow: [8, -24], // 下偏移
      green: [0, -16],  // 下偏移
      cyan: [-8, -8],   // 左下偏移
      blue: [0, 0],     // 无偏移
      purple: [8, 8],   // 右上偏移
      pink: [16, 0],    // 右偏移
      black: [8, -8],   // 右下偏移
    }
  },
  I: {
    defaultOffset: [-8, 8],
    level1Offsets: {
      red: [-8, 8],     // 左上偏移
      orange: [-16, 16], // 大左上偏移
      yellow: [-8, 24], // 上偏移
      green: [0, 16],   // 上偏移
      cyan: [8, 8],     // 右上偏移
      blue: [0, 0],     // 无偏移
      purple: [-8, -8], // 左下偏移
      pink: [-16, 0],   // 左偏移
      black: [-8, 8],   // 左上偏移
    }
  },
  J: {
    defaultOffset: [12, 12],
    level1Offsets: {
      red: [12, 12],    // 右上偏移
      orange: [16, 16], // 大右上偏移
      yellow: [12, 20], // 上偏移
      green: [8, 16],   // 右上偏移
      cyan: [4, 12],    // 上偏移
      blue: [8, 8],     // 右上偏移
      purple: [12, 4],  // 右偏移
      pink: [16, 8],    // 右偏移
      black: [12, 12],  // 右上偏移
    }
  },
  K: {
    defaultOffset: [-12, -12],
    level1Offsets: {
      red: [-12, -12],  // 左下偏移
      orange: [-16, -16], // 大左下偏移
      yellow: [-12, -20], // 下偏移
      green: [-8, -16], // 左下偏移
      cyan: [-4, -12],  // 下偏移
      blue: [-8, -8],   // 左下偏移
      purple: [-12, -4], // 左偏移
      pink: [-16, -8],  // 左偏移
      black: [-12, -12], // 左下偏移
    }
  },
  L: {
    defaultOffset: [12, -12],
    level1Offsets: {
      red: [12, -12],   // 右下偏移
      orange: [16, -16], // 大右下偏移
      yellow: [12, -20], // 下偏移
      green: [8, -16],  // 右下偏移
      cyan: [4, -12],   // 下偏移
      blue: [8, -8],    // 右下偏移
      purple: [12, -4], // 右偏移
      pink: [16, -8],   // 右偏移
      black: [12, -12], // 右下偏移
    }
  },
  M: {
    defaultOffset: [-12, 12],
    level1Offsets: {
      red: [-12, 12],   // 左上偏移
      orange: [-16, 16], // 大左上偏移
      yellow: [-12, 20], // 上偏移
      green: [-8, 16],  // 左上偏移
      cyan: [-4, 12],   // 上偏移
      blue: [-8, 8],    // 左上偏移
      purple: [-12, 4], // 左偏移
      pink: [-16, 8],   // 左偏移
      black: [-12, 12], // 左上偏移
    }
  },
};

// ===== 数据处理工具函数 =====

/** 坐标键生成 */
export const coordinateKey = (x: number, y: number): string => `${x},${y}`;

/** 将相对坐标转换为绝对坐标 */
export const toAbsoluteCoordinate = (relativeX: number, relativeY: number): [number, number] => {
  return [GRID_CENTER.X + relativeX, GRID_CENTER.Y + relativeY];
};

/** 应用偏移到坐标 */
export const applyOffset = (x: number, y: number, offset: [number, number]): [number, number] => {
  return [x + offset[0], y + offset[1]];
};

/** 检查坐标是否在网格范围内 */
export const isValidCoordinate = (x: number, y: number): boolean => {
  return x >= 0 && x < MATRIX_SIZE && y >= 0 && y < MATRIX_SIZE;
};

// ===== 数据生成函数 =====

/** 从A组基础数据生成指定组的数据点 */
export const generateGroupData = (group: GroupType = 'A'): MatrixDataPoint[] => {
  const points: MatrixDataPoint[] = [];
  const config = GROUP_OFFSET_CONFIGS[group];

  // 遍历所有颜色和级别
  Object.entries(GROUP_A_DATA).forEach(([colorKey, levels]) => {
    const color = colorKey as BasicColorType;

    Object.entries(levels).forEach(([levelKey, coordinates]) => {
      const level = parseInt(levelKey) as DataLevel;

      coordinates.forEach((relativeCoord, index) => {
        // 转换为绝对坐标
        const [baseX, baseY] = toAbsoluteCoordinate(relativeCoord[0], relativeCoord[1]);

        // 应用组偏移
        let offset = config.defaultOffset;
        if (config.level1Offsets && level === 1) {
          offset = config.level1Offsets[color] || config.defaultOffset;
        }

        const [finalX, finalY] = applyOffset(baseX, baseY, offset);

        // 检查坐标有效性
        if (isValidCoordinate(finalX, finalY)) {
          points.push({
            x: finalX,
            y: finalY,
            color,
            level,
            group,
            id: `${group.toLowerCase()}${level}-${color}-${index + 1}`
          });
        }
      });
    });
  });

  return points;
};

/** 创建完整的矩阵数据集 */
export const createMatrixDataSet = (groups: GroupType[] = ['A']): MatrixDataSet => {
  const allPoints: MatrixDataPoint[] = [];

  // 生成所有组的数据
  groups.forEach(group => {
    const groupPoints = generateGroupData(group);
    allPoints.push(...groupPoints);
  });

  // 创建索引
  const byColor = new Map<BasicColorType, MatrixDataPoint[]>();
  const byLevel = new Map<DataLevel, MatrixDataPoint[]>();
  const byGroup = new Map<GroupType, MatrixDataPoint[]>();
  const byCoordinate = new Map<string, MatrixDataPoint>();

  // 初始化计数器
  const colorCounts: Record<BasicColorType, number> = {
    black: 0, red: 0, cyan: 0, yellow: 0, purple: 0,
    orange: 0, green: 0, blue: 0, pink: 0
  };
  const levelCounts: Record<DataLevel, number> = { 1: 0, 2: 0, 3: 0, 4: 0 };
  const groupCounts: Record<GroupType, number> = {
    A: 0, B: 0, C: 0, D: 0, E: 0, F: 0, G: 0, H: 0, I: 0, J: 0, K: 0, L: 0, M: 0
  };

  // 处理每个数据点
  allPoints.forEach(point => {
    // 按颜色分组
    if (!byColor.has(point.color)) {
      byColor.set(point.color, []);
    }
    byColor.get(point.color)!.push(point);

    // 按层级分组
    if (!byLevel.has(point.level)) {
      byLevel.set(point.level, []);
    }
    byLevel.get(point.level)!.push(point);

    // 按组分组
    if (!byGroup.has(point.group)) {
      byGroup.set(point.group, []);
    }
    byGroup.get(point.group)!.push(point);

    // 按坐标索引
    const key = coordinateKey(point.x, point.y);
    byCoordinate.set(key, point);

    // 统计计数
    colorCounts[point.color]++;
    levelCounts[point.level]++;
    groupCounts[point.group]++;
  });

  return {
    points: allPoints,
    byColor,
    byLevel,
    byGroup,
    byCoordinate,
    metadata: {
      totalPoints: allPoints.length,
      colorCounts,
      levelCounts,
      groupCounts,
      lastUpdate: Date.now()
    }
  };
};

// ===== 查询工具函数 =====

/** 根据坐标获取数据点 */
export const getMatrixDataByCoordinate = (
  dataSet: MatrixDataSet,
  x: number,
  y: number
): MatrixDataPoint | null => {
  const key = coordinateKey(x, y);
  return dataSet.byCoordinate.get(key) || null;
};

/** 根据颜色获取数据点 */
export const getMatrixDataByColor = (
  dataSet: MatrixDataSet,
  color: BasicColorType
): MatrixDataPoint[] => {
  return dataSet.byColor.get(color) || [];
};

/** 根据层级获取数据点 */
export const getMatrixDataByLevel = (
  dataSet: MatrixDataSet,
  level: DataLevel
): MatrixDataPoint[] => {
  return dataSet.byLevel.get(level) || [];
};

/** 根据组获取数据点 */
export const getMatrixDataByGroup = (
  dataSet: MatrixDataSet,
  group: GroupType
): MatrixDataPoint[] => {
  return dataSet.byGroup.get(group) || [];
};

/** 检查坐标是否包含数据 */
export const hasMatrixData = (
  dataSet: MatrixDataSet,
  x: number,
  y: number
): boolean => {
  const key = coordinateKey(x, y);
  return dataSet.byCoordinate.has(key);
};

// ===== 数据验证和统计函数 =====

/** 验证数据集完整性 */
export const validateMatrixDataSet = (dataSet: MatrixDataSet): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查数据点是否在有效范围内
  dataSet.points.forEach(point => {
    if (!isValidCoordinate(point.x, point.y)) {
      errors.push(`数据点 ${point.id} 坐标 (${point.x}, ${point.y}) 超出网格范围`);
    }
  });

  // 检查是否有重复坐标
  const coordinateMap = new Map<string, MatrixDataPoint[]>();
  dataSet.points.forEach(point => {
    const key = coordinateKey(point.x, point.y);
    if (!coordinateMap.has(key)) {
      coordinateMap.set(key, []);
    }
    coordinateMap.get(key)!.push(point);
  });

  coordinateMap.forEach((points, coordinate) => {
    if (points.length > 1) {
      warnings.push(`坐标 ${coordinate} 有 ${points.length} 个重叠数据点: ${points.map(p => p.id).join(', ')}`);
    }
  });

  // 检查每个组是否有数据
  const allGroups: GroupType[] = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'];
  allGroups.forEach(group => {
    const groupData = getMatrixDataByGroup(dataSet, group);
    if (groupData.length === 0) {
      warnings.push(`组 ${group} 没有数据点`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/** 获取数据集统计信息 */
export const getMatrixDataStatistics = (dataSet: MatrixDataSet) => {
  const stats = {
    总数据点: dataSet.metadata.totalPoints,
    按颜色统计: dataSet.metadata.colorCounts,
    按级别统计: dataSet.metadata.levelCounts,
    按组统计: dataSet.metadata.groupCounts,
    坐标范围: {
      x最小值: Math.min(...dataSet.points.map(p => p.x)),
      x最大值: Math.max(...dataSet.points.map(p => p.x)),
      y最小值: Math.min(...dataSet.points.map(p => p.y)),
      y最大值: Math.max(...dataSet.points.map(p => p.y)),
    },
    密度分析: {
      网格利用率: `${((dataSet.metadata.totalPoints / (MATRIX_SIZE * MATRIX_SIZE)) * 100).toFixed(2)}%`,
      平均每组数据点: Math.round(dataSet.metadata.totalPoints / Object.keys(dataSet.metadata.groupCounts).filter(g => dataSet.metadata.groupCounts[g as GroupType] > 0).length),
    }
  };

  return stats;
};

/** 生成数据分布热力图数据 */
export const generateHeatmapData = (dataSet: MatrixDataSet): number[][] => {
  const heatmap: number[][] = Array(MATRIX_SIZE).fill(null).map(() => Array(MATRIX_SIZE).fill(0));

  dataSet.points.forEach(point => {
    if (isValidCoordinate(point.x, point.y)) {
      heatmap[point.y][point.x]++;
    }
  });

  return heatmap;
};

/** 检查特定组的数据完整性 */
export const checkGroupCompleteness = (group: GroupType): {
  expectedPoints: number;
  actualPoints: number;
  completeness: number;
  missingData: string[];
} => {
  const groupData = generateGroupData(group);
  const expectedPoints = Object.values(GROUP_A_DATA).reduce((total, levels) => {
    return total + Object.values(levels).reduce((levelTotal, coords) => levelTotal + coords.length, 0);
  }, 0);

  const missingData: string[] = [];

  // 检查每种颜色每个级别是否都有数据
  Object.entries(GROUP_A_DATA).forEach(([color, levels]) => {
    Object.keys(levels).forEach(level => {
      const hasData = groupData.some(point =>
        point.color === color && point.level === parseInt(level)
      );
      if (!hasData) {
        missingData.push(`${group}组 ${color} 级别${level}`);
      }
    });
  });

  return {
    expectedPoints,
    actualPoints: groupData.length,
    completeness: (groupData.length / expectedPoints) * 100,
    missingData
  };
};

// ===== 默认数据集实例 =====

/** 默认的优化A组数据集 */
export const OPTIMIZED_GROUP_A_DATA = createMatrixDataSet(['A']);

/** 完整的A-M组数据集 */
export const COMPLETE_MATRIX_DATA = createMatrixDataSet(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M']);

// ===== 导出 =====

export default {
  // 基础数据
  GROUP_A_DATA,
  AVAILABLE_LEVELS,
  DEFAULT_COLOR_VALUES,
  GROUP_OFFSET_CONFIGS,

  // 预生成数据集
  OPTIMIZED_GROUP_A_DATA,
  COMPLETE_MATRIX_DATA,

  // 数据生成函数
  createMatrixDataSet,
  generateGroupData,

  // 查询函数
  getMatrixDataByCoordinate,
  getMatrixDataByColor,
  getMatrixDataByLevel,
  getMatrixDataByGroup,
  hasMatrixData,

  // 工具函数
  coordinateKey,
  toAbsoluteCoordinate,
  applyOffset,
  isValidCoordinate,

  // 验证和统计函数
  validateMatrixDataSet,
  getMatrixDataStatistics,
  generateHeatmapData,
  checkGroupCompleteness,
};
